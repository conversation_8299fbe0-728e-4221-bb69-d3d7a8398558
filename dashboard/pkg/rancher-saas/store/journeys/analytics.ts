/**
 * Journey Analytics Persistence
 * 
 * This module handles analytics data collection, aggregation, and persistence
 * for journey tracking and optimization.
 */

import type { JourneyProgress, JourneyEventPayload } from '../../types/journey';

export interface AnalyticsData {
  journeyStartCount: Record<string, number>;
  journeyCompletionCount: Record<string, number>;
  journeySkipCount: Record<string, number>;
  averageCompletionTime: Record<string, number>;
  dropOffPoints: Record<string, number>;
  popularChoices: Record<string, number>;
  userPaths: Array<{
    journeyId: string;
    steps: string[];
    decisions: Record<string, any>;
    duration: number;
    timestamp: Date;
  }>;
}

export interface AnalyticsEvent {
  type: 'journey-start' | 'journey-complete' | 'journey-skip' | 'step-enter' | 'decision-made' | 'drop-off';
  journeyId: string;
  stepId?: string;
  choiceId?: string;
  duration?: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export class JourneyAnalytics {
  private store: any;
  private events: AnalyticsEvent[] = [];
  private batchSize = 10;
  private flushInterval = 60000; // 1 minute
  private flushTimer: NodeJS.Timeout | null = null;

  constructor(store: any) {
    this.store = store;
    this.startPeriodicFlush();
  }

  /**
   * Record a journey analytics event
   */
  recordEvent(event: Omit<AnalyticsEvent, 'timestamp'>): void {
    const analyticsEvent: AnalyticsEvent = {
      ...event,
      timestamp: new Date(),
    };

    this.events.push(analyticsEvent);

    // Process event immediately for real-time updates
    this.processEvent(analyticsEvent);

    // Batch flush if we have enough events
    if (this.events.length >= this.batchSize) {
      this.flushEvents();
    }
  }

  /**
   * Record journey start
   */
  recordJourneyStart(journeyId: string, metadata?: Record<string, any>): void {
    this.recordEvent({
      type: 'journey-start',
      journeyId,
      metadata,
    });

    // Update store immediately
    this.store.commit('journeys/INCREMENT_JOURNEY_START_COUNT', journeyId);
  }

  /**
   * Record journey completion
   */
  recordJourneyCompletion(journeyId: string, duration: number, metadata?: Record<string, any>): void {
    this.recordEvent({
      type: 'journey-complete',
      journeyId,
      duration,
      metadata,
    });

    // Update store immediately
    this.store.commit('journeys/INCREMENT_JOURNEY_COMPLETION_COUNT', journeyId);
    this.store.commit('journeys/UPDATE_AVERAGE_COMPLETION_TIME', { journeyId, duration });
  }

  /**
   * Record journey skip
   */
  recordJourneySkip(journeyId: string, stepId?: string, metadata?: Record<string, any>): void {
    this.recordEvent({
      type: 'journey-skip',
      journeyId,
      stepId,
      metadata,
    });

    // Update store immediately
    this.store.commit('journeys/INCREMENT_JOURNEY_SKIP_COUNT', journeyId);
    
    if (stepId) {
      this.store.commit('journeys/ADD_DROP_OFF_POINT', { journeyId, stepId });
    }
  }

  /**
   * Record step entry
   */
  recordStepEntry(journeyId: string, stepId: string, metadata?: Record<string, any>): void {
    this.recordEvent({
      type: 'step-enter',
      journeyId,
      stepId,
      metadata,
    });
  }

  /**
   * Record decision made
   */
  recordDecision(journeyId: string, stepId: string, choiceId: string, metadata?: Record<string, any>): void {
    this.recordEvent({
      type: 'decision-made',
      journeyId,
      stepId,
      choiceId,
      metadata,
    });

    // Update store immediately
    this.store.commit('journeys/RECORD_CHOICE_SELECTION', { journeyId, stepId, choiceId });
  }

  /**
   * Record user path completion
   */
  recordUserPath(progress: JourneyProgress): void {
    if (!progress.startedAt || !progress.completedAt) return;

    const duration = Math.floor(
      (new Date(progress.completedAt).getTime() - new Date(progress.startedAt).getTime()) / (1000 * 60)
    );

    const userPath = {
      journeyId: progress.journeyId,
      steps: progress.completedSteps,
      decisions: progress.decisions,
      duration,
      timestamp: new Date(),
    };

    this.store.commit('journeys/ADD_USER_PATH', userPath);
  }

  /**
   * Process individual event for real-time analytics
   */
  private processEvent(event: AnalyticsEvent): void {
    // Additional real-time processing can be added here
    // For example, triggering alerts for high drop-off rates
    
    if (event.type === 'drop-off') {
      this.checkDropOffRate(event.journeyId, event.stepId);
    }
  }

  /**
   * Check drop-off rate for a specific step
   */
  private checkDropOffRate(journeyId: string, stepId?: string): void {
    if (!stepId) return;

    const state = this.store.state.journeys;
    const dropOffKey = `${journeyId}:${stepId}`;
    const dropOffs = state.analytics.dropOffPoints[dropOffKey] || 0;
    const starts = state.analytics.journeyStartCount[journeyId] || 0;

    if (starts > 0) {
      const dropOffRate = (dropOffs / starts) * 100;
      
      // Alert if drop-off rate is high (>50%)
      if (dropOffRate > 50) {
        console.warn(`High drop-off rate detected: ${dropOffRate.toFixed(1)}% at ${journeyId}:${stepId}`);
        
        // Could trigger additional actions like:
        // - Sending analytics to external service
        // - Showing admin notifications
        // - Adjusting journey flow
      }
    }
  }

  /**
   * Flush events to persistent storage
   */
  private async flushEvents(): Promise<void> {
    if (this.events.length === 0) return;

    try {
      // Get current analytics data
      const currentAnalytics = this.store.state.journeys.analytics;
      
      // Save to preferences
      await this.store.dispatch('journeys/saveAnalytics', currentAnalytics);
      
      // Clear processed events
      this.events = [];
      
      console.log('Analytics events flushed to storage');
    } catch (error) {
      console.error('Failed to flush analytics events:', error);
    }
  }

  /**
   * Start periodic flush timer
   */
  private startPeriodicFlush(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }

    this.flushTimer = setInterval(() => {
      this.flushEvents();
    }, this.flushInterval);
  }

  /**
   * Get analytics summary for a journey
   */
  getJourneySummary(journeyId: string): any {
    const state = this.store.state.journeys;
    const analytics = state.analytics;

    const starts = analytics.journeyStartCount[journeyId] || 0;
    const completions = analytics.journeyCompletionCount[journeyId] || 0;
    const skips = analytics.journeySkipCount[journeyId] || 0;
    const avgTime = analytics.averageCompletionTime[journeyId] || 0;

    const completionRate = starts > 0 ? (completions / starts) * 100 : 0;
    const skipRate = starts > 0 ? (skips / starts) * 100 : 0;

    return {
      journeyId,
      starts,
      completions,
      skips,
      completionRate: Math.round(completionRate * 100) / 100,
      skipRate: Math.round(skipRate * 100) / 100,
      averageCompletionTime: Math.round(avgTime * 100) / 100,
    };
  }

  /**
   * Get drop-off analysis for a journey
   */
  getDropOffAnalysis(journeyId: string): any {
    const state = this.store.state.journeys;
    const analytics = state.analytics;
    const journey = state.definitions[journeyId];

    if (!journey) return null;

    const dropOffs: any[] = [];
    const starts = analytics.journeyStartCount[journeyId] || 0;

    journey.steps.forEach((step: any) => {
      const dropOffKey = `${journeyId}:${step.id}`;
      const dropOffCount = analytics.dropOffPoints[dropOffKey] || 0;
      const dropOffRate = starts > 0 ? (dropOffCount / starts) * 100 : 0;

      dropOffs.push({
        stepId: step.id,
        stepName: step.name,
        dropOffCount,
        dropOffRate: Math.round(dropOffRate * 100) / 100,
      });
    });

    return {
      journeyId,
      totalStarts: starts,
      dropOffs: dropOffs.sort((a, b) => b.dropOffRate - a.dropOffRate),
    };
  }

  /**
   * Get popular choices analysis
   */
  getPopularChoicesAnalysis(journeyId: string): any {
    const state = this.store.state.journeys;
    const analytics = state.analytics;
    const journey = state.definitions[journeyId];

    if (!journey) return null;

    const choiceAnalysis: any[] = [];

    journey.steps.forEach((step: any) => {
      if (step.type === 'decision' && step.choices) {
        const stepChoices: any[] = [];
        let totalChoices = 0;

        step.choices.forEach((choice: any) => {
          const choiceKey = `${journeyId}:${step.id}:${choice.id}`;
          const count = analytics.popularChoices[choiceKey] || 0;
          totalChoices += count;

          stepChoices.push({
            choiceId: choice.id,
            choiceLabel: choice.label,
            count,
          });
        });

        // Calculate percentages
        stepChoices.forEach(choice => {
          choice.percentage = totalChoices > 0 ? 
            Math.round((choice.count / totalChoices) * 100 * 100) / 100 : 0;
        });

        choiceAnalysis.push({
          stepId: step.id,
          stepName: step.name,
          totalChoices,
          choices: stepChoices.sort((a, b) => b.count - a.count),
        });
      }
    });

    return {
      journeyId,
      choiceAnalysis,
    };
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }

    // Flush any remaining events
    this.flushEvents();
  }
}

/**
 * Create analytics manager instance
 */
export function createAnalyticsManager(store: any): JourneyAnalytics {
  return new JourneyAnalytics(store);
}
