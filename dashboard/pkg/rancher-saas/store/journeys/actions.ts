import type {
  JourneyState,
  JourneyDefinition,
  JourneyProgress,
  JourneyAction,
  JourneyContext,
  JourneyEventPayload
} from '../../types/journey';

import { JourneyPersistence } from './persistence';
import { createPreferencesManager } from './preferences';
import { createAnalyticsManager } from './analytics';

// Initialize persistence and analytics managers
let persistenceManager: JourneyPersistence | null = null;
let preferencesManager: any = null;
let analyticsManager: any = null;

export default {
  // Initialize persistence system
  async initializePersistence(context: any) {
    if (!persistenceManager) {
      // Create a store-like object for the persistence managers
      const storeProxy = {
        dispatch: (action: string, payload?: any, options?: any) => {
          if (options?.root) {
            return context.dispatch(action, payload, options);
          }
          return context.dispatch(`journeys/${action}`, payload);
        },
        getters: context.rootGetters,
        state: context.rootState,
      };

      persistenceManager = new JourneyPersistence(storeProxy, {
        useUserPreferences: true,
        useLocalStorage: true,
        syncInterval: 30000, // 30 seconds
      });

      preferencesManager = createPreferencesManager(storeProxy);
      analyticsManager = createAnalyticsManager(storeProxy);

      await persistenceManager.initialize();
    }
  },

  // Journey Management Actions
  async loadJourneyDefinitions({ commit, dispatch }: any) {
    try {
      commit('SET_JOURNEY_LOADING', true);

      // Initialize persistence if not already done
      await dispatch('initializePersistence');

      // Load journey definitions from configuration
      const { default: journeyDefinitions } = await import('./definitions');

      commit('SET_JOURNEY_DEFINITIONS', journeyDefinitions);
      commit('CLEAR_CACHE'); // Clear cache when definitions change

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    } finally {
      commit('SET_JOURNEY_LOADING', false);
    }
  },

  async startJourney({ commit, dispatch, state, getters }: any, { journeyId, context }: { journeyId: string, context?: Partial<JourneyContext> }) {
    try {
      const journey = getters.getJourneyById(journeyId);
      if (!journey) {
        throw new Error(`Journey not found: ${journeyId}`);
      }

      // Check if journey is already in progress
      const existingProgress = getters.getProgressById(journeyId);
      if (existingProgress?.status === 'in-progress') {
        return dispatch('resumeJourney', journeyId);
      }

      // Create new progress
      const progress: JourneyProgress = {
        journeyId,
        status: 'in-progress',
        currentStepId: journey.steps[0]?.id,
        currentStepIndex: 0,
        completedSteps: [],
        decisions: {},
        startedAt: new Date(),
        lastActiveAt: new Date(),
        metadata: context || {},
      };

      // Set current journey and progress
      commit('SET_CURRENT_JOURNEY', journey);
      commit('SET_CURRENT_PROGRESS', progress);
      commit('SET_USER_PROGRESS', { journeyId, progress });
      commit('SET_JOURNEY_ACTIVE', true);

      // Analytics
      commit('INCREMENT_JOURNEY_START_COUNT', journeyId);
      if (analyticsManager) {
        analyticsManager.recordJourneyStart(journeyId, context);
      }

      // Execute journey start hook
      if (journey.onStart) {
        const journeyContext = await dispatch('buildJourneyContext', { journey, progress, context });
        await journey.onStart(journeyContext);
      }

      // Emit event
      dispatch('emitJourneyEvent', {
        event: 'journey-started',
        journeyId,
        data: { journey, progress },
      });

      return progress;

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  async resumeJourney({ commit, dispatch, state, getters }: any, journeyId: string) {
    try {
      const journey = getters.getJourneyById(journeyId);
      const progress = getters.getProgressById(journeyId);

      if (!journey || !progress) {
        throw new Error(`Cannot resume journey: ${journeyId}`);
      }

      if (progress.status !== 'in-progress') {
        throw new Error(`Journey is not in progress: ${journeyId}`);
      }

      // Set as current journey
      commit('SET_CURRENT_JOURNEY', journey);
      commit('SET_CURRENT_PROGRESS', progress);
      commit('SET_JOURNEY_ACTIVE', true);

      // Update last active time
      commit('UPDATE_USER_PROGRESS', { 
        journeyId, 
        updates: { lastActiveAt: new Date() } 
      });

      return progress;

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  async completeJourney({ commit, dispatch, state, getters }: any, journeyId: string) {
    try {
      const journey = getters.getJourneyById(journeyId);
      const progress = getters.getProgressById(journeyId);

      if (!journey || !progress) {
        throw new Error(`Cannot complete journey: ${journeyId}`);
      }

      const completedAt = new Date();
      const duration = progress.startedAt ? 
        Math.floor((completedAt.getTime() - new Date(progress.startedAt).getTime()) / (1000 * 60)) : 0;

      // Update progress
      const updatedProgress = {
        ...progress,
        status: 'completed' as const,
        completedAt,
        lastActiveAt: completedAt,
      };

      commit('SET_USER_PROGRESS', { journeyId, progress: updatedProgress });
      commit('ADD_JOURNEY_HISTORY', updatedProgress);

      // Analytics
      commit('INCREMENT_JOURNEY_COMPLETION_COUNT', journeyId);
      commit('UPDATE_AVERAGE_COMPLETION_TIME', { journeyId, duration });
      commit('ADD_USER_PATH', {
        journeyId,
        steps: progress.completedSteps,
        decisions: progress.decisions,
        duration,
      });

      // Execute journey completion hook
      if (journey.onComplete) {
        const journeyContext = await dispatch('buildJourneyContext', { journey, progress: updatedProgress });
        await journey.onComplete(journeyContext);
      }

      // Clear current journey if it's the one being completed
      if (state.currentJourney?.id === journeyId) {
        commit('SET_CURRENT_JOURNEY', null);
        commit('SET_CURRENT_PROGRESS', null);
        commit('SET_JOURNEY_ACTIVE', false);
      }

      // Emit event
      dispatch('emitJourneyEvent', {
        event: 'journey-completed',
        journeyId,
        data: { journey, progress: updatedProgress, duration },
      });

      return updatedProgress;

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  async skipJourney({ commit, dispatch, state, getters }: any, journeyId: string) {
    try {
      const journey = getters.getJourneyById(journeyId);
      const progress = getters.getProgressById(journeyId);

      if (!journey || !progress) {
        throw new Error(`Cannot skip journey: ${journeyId}`);
      }

      const skippedAt = new Date();
      const updatedProgress = {
        ...progress,
        status: 'skipped' as const,
        completedAt: skippedAt,
        lastActiveAt: skippedAt,
      };

      commit('SET_USER_PROGRESS', { journeyId, progress: updatedProgress });
      commit('ADD_JOURNEY_HISTORY', updatedProgress);

      // Analytics
      commit('INCREMENT_JOURNEY_SKIP_COUNT', journeyId);
      if (progress.currentStepId) {
        commit('ADD_DROP_OFF_POINT', { journeyId, stepId: progress.currentStepId });
      }

      // Execute journey skip hook
      if (journey.onSkip) {
        const journeyContext = await dispatch('buildJourneyContext', { journey, progress: updatedProgress });
        await journey.onSkip(journeyContext);
      }

      // Clear current journey if it's the one being skipped
      if (state.currentJourney?.id === journeyId) {
        commit('SET_CURRENT_JOURNEY', null);
        commit('SET_CURRENT_PROGRESS', null);
        commit('SET_JOURNEY_ACTIVE', false);
      }

      // Emit event
      dispatch('emitJourneyEvent', {
        event: 'journey-skipped',
        journeyId,
        data: { journey, progress: updatedProgress },
      });

      return updatedProgress;

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  async restartJourney({ dispatch }: any, journeyId: string) {
    try {
      // Skip current journey if in progress
      await dispatch('skipJourney', journeyId);
      
      // Start fresh
      await dispatch('startJourney', { journeyId });

    } catch (error) {
      throw error;
    }
  },

  // Step Navigation Actions
  async nextStep({ commit, dispatch, state, getters }: any) {
    try {
      if (!state.currentJourney || !state.currentProgress) {
        throw new Error('No active journey');
      }

      const nextStep = getters.getNextStep;
      if (!nextStep) {
        // Journey is complete
        return dispatch('completeJourney', state.currentJourney.id);
      }

      const currentStepIndex = getters.getCurrentStepIndex;
      const newStepIndex = currentStepIndex + 1;

      // Mark current step as completed
      if (state.currentProgress.currentStepId) {
        commit('ADD_COMPLETED_STEP', {
          journeyId: state.currentJourney.id,
          stepId: state.currentProgress.currentStepId,
        });
      }

      // Move to next step
      commit('SET_CURRENT_STEP', {
        journeyId: state.currentJourney.id,
        stepId: nextStep.id,
        stepIndex: newStepIndex,
      });

      // Save progress
      await dispatch('saveProgress', state.currentProgress);

      // Emit event
      dispatch('emitJourneyEvent', {
        event: 'step-entered',
        journeyId: state.currentJourney.id,
        stepId: nextStep.id,
        data: { step: nextStep, stepIndex: newStepIndex },
      });

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  async previousStep({ commit, dispatch, state, getters }: any) {
    try {
      if (!state.currentJourney || !state.currentProgress) {
        throw new Error('No active journey');
      }

      const previousStep = getters.getPreviousStep;
      if (!previousStep) {
        throw new Error('No previous step available');
      }

      const currentStepIndex = getters.getCurrentStepIndex;
      const newStepIndex = currentStepIndex - 1;

      // Move to previous step
      commit('SET_CURRENT_STEP', {
        journeyId: state.currentJourney.id,
        stepId: previousStep.id,
        stepIndex: newStepIndex,
      });

      // Save progress
      await dispatch('saveProgress', state.currentProgress);

      // Emit event
      dispatch('emitJourneyEvent', {
        event: 'step-entered',
        journeyId: state.currentJourney.id,
        stepId: previousStep.id,
        data: { step: previousStep, stepIndex: newStepIndex },
      });

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  async goToStep({ commit, dispatch, state, getters }: any, stepId: string) {
    try {
      if (!state.currentJourney || !state.currentProgress) {
        throw new Error('No active journey');
      }

      const step = state.currentJourney.steps.find((s: any) => s.id === stepId);
      if (!step) {
        throw new Error(`Step not found: ${stepId}`);
      }

      const stepIndex = state.currentJourney.steps.indexOf(step);

      // Move to specified step
      commit('SET_CURRENT_STEP', {
        journeyId: state.currentJourney.id,
        stepId: step.id,
        stepIndex,
      });

      // Save progress
      await dispatch('saveProgress', state.currentProgress);

      // Emit event
      dispatch('emitJourneyEvent', {
        event: 'step-entered',
        journeyId: state.currentJourney.id,
        stepId: step.id,
        data: { step, stepIndex },
      });

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  // Decision Handling
  async makeDecision({ commit, dispatch, state }: any, { stepId, choiceId, data }: { stepId: string, choiceId: string, data?: any }) {
    try {
      if (!state.currentJourney || !state.currentProgress) {
        throw new Error('No active journey');
      }

      // Record the decision
      commit('ADD_DECISION', {
        journeyId: state.currentJourney.id,
        stepId,
        choiceId,
        data,
      });

      // Analytics
      commit('RECORD_CHOICE_SELECTION', {
        journeyId: state.currentJourney.id,
        stepId,
        choiceId,
      });

      // Save progress
      await dispatch('saveProgress', state.currentProgress);

      // Emit event
      dispatch('emitJourneyEvent', {
        event: 'decision-made',
        journeyId: state.currentJourney.id,
        stepId,
        data: { choiceId, data },
      });

      // Handle choice navigation
      const step = state.currentJourney.steps.find((s: any) => s.id === stepId);
      const choice = step?.choices?.find((c: any) => c.id === choiceId);

      if (choice?.next) {
        await dispatch('goToStep', choice.next);
      } else if (choice?.action) {
        await dispatch('executeAction', { action: choice.action, context: { choice, step } });
      }

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  // Workflow Execution
  async executeAction({ commit, dispatch, state, rootState }: any, { action, context }: { action: JourneyAction, context: JourneyContext }) {
    try {
      let result;

      switch (action.type) {
        case 'navigate':
          result = await dispatch('executeNavigateAction', { action, context }, { root: true });
          break;
        case 'create-resource':
          result = await dispatch('executeCreateResourceAction', { action, context }, { root: true });
          break;
        case 'update-resource':
          result = await dispatch('executeUpdateResourceAction', { action, context }, { root: true });
          break;
        case 'trigger-workflow':
          result = await dispatch('executeTriggerWorkflowAction', { action, context }, { root: true });
          break;
        case 'show-modal':
          result = await dispatch('executeShowModalAction', { action, context }, { root: true });
          break;
        case 'custom':
          if (action.customHandler) {
            result = await action.customHandler(context);
          } else {
            throw new Error('Custom action handler not defined');
          }
          break;
        default:
          throw new Error(`Unknown action type: ${action.type}`);
      }

      // Emit event
      dispatch('emitJourneyEvent', {
        event: 'action-executed',
        journeyId: state.currentJourney?.id,
        data: { action, result, context },
      });

      return result;

    } catch (error) {
      commit('ADD_ERROR', error);
      throw error;
    }
  },

  // Progress Management
  async updateProgress({ commit, dispatch }: any, { journeyId, progress }: { journeyId: string, progress: Partial<JourneyProgress> }) {
    commit('UPDATE_USER_PROGRESS', { journeyId, updates: progress });

    // Auto-save progress
    const updatedProgress = { ...progress, journeyId } as JourneyProgress;
    await dispatch('saveProgress', updatedProgress);
  },

  async saveProgress({ dispatch }: any, progress: JourneyProgress) {
    try {
      // Initialize persistence if needed
      await dispatch('initializePersistence');

      // Save using persistence manager
      if (persistenceManager) {
        await persistenceManager.saveProgress(progress);
      }

      // Also save using preferences manager for additional reliability
      if (preferencesManager) {
        await preferencesManager.saveProgress(progress);
      }

    } catch (error) {
      console.error('Failed to save journey progress:', error);
      throw error;
    }
  },

  async loadProgress({ commit, dispatch }: any) {
    try {
      // Initialize persistence if needed
      await dispatch('initializePersistence');

      let progress = null;

      // Try preferences manager first
      if (preferencesManager) {
        progress = await preferencesManager.loadProgress();
      }

      // Fallback to persistence manager
      if (!progress && persistenceManager) {
        progress = await persistenceManager.loadProgress();
      }

      if (progress && typeof progress === 'object') {
        Object.keys(progress).forEach(journeyId => {
          commit('SET_USER_PROGRESS', { journeyId, progress: progress[journeyId] });
        });
      }

    } catch (error) {
      console.error('Failed to load journey progress:', error);
    }
  },

  async saveConfig({ dispatch }: any, config: Partial<JourneyState['config']>) {
    try {
      if (preferencesManager) {
        await preferencesManager.saveConfig(config);
      }
    } catch (error) {
      console.error('Failed to save journey config:', error);
    }
  },

  async loadConfig({ commit, dispatch }: any) {
    try {
      await dispatch('initializePersistence');

      if (preferencesManager) {
        const config = await preferencesManager.loadConfig();
        if (config) {
          commit('SET_JOURNEY_CONFIG', config);
        }
      }
    } catch (error) {
      console.error('Failed to load journey config:', error);
    }
  },

  async saveAnalytics({ dispatch }: any, analytics: Partial<JourneyState['analytics']>) {
    try {
      if (preferencesManager) {
        await preferencesManager.saveAnalytics(analytics);
      }
    } catch (error) {
      console.error('Failed to save journey analytics:', error);
    }
  },

  async loadAnalytics({ dispatch }: any) {
    try {
      await dispatch('initializePersistence');

      if (preferencesManager) {
        return await preferencesManager.loadAnalytics();
      }
      return null;
    } catch (error) {
      console.error('Failed to load journey analytics:', error);
      return null;
    }
  },

  async saveSystemState({ dispatch }: any, systemState: Record<string, any>) {
    try {
      if (preferencesManager) {
        await preferencesManager.saveSystemState(systemState);
      }
    } catch (error) {
      console.error('Failed to save system state:', error);
    }
  },

  async loadSystemState({ commit, dispatch }: any) {
    try {
      await dispatch('initializePersistence');

      if (preferencesManager) {
        const systemState = await preferencesManager.loadSystemState();
        if (systemState) {
          commit('SET_SYSTEM_STATE', systemState);
        }
      }
    } catch (error) {
      console.error('Failed to load system state:', error);
    }
  },

  async exportJourneyData({ dispatch }: any) {
    try {
      await dispatch('initializePersistence');

      if (preferencesManager) {
        return await preferencesManager.exportData();
      }
      return null;
    } catch (error) {
      console.error('Failed to export journey data:', error);
      throw error;
    }
  },

  async importJourneyData({ dispatch }: any, data: any) {
    try {
      await dispatch('initializePersistence');

      if (preferencesManager) {
        await preferencesManager.importData(data);

        // Reload all data after import
        await dispatch('loadProgress');
        await dispatch('loadConfig');
        await dispatch('loadSystemState');
      }
    } catch (error) {
      console.error('Failed to import journey data:', error);
      throw error;
    }
  },

  async clearAllJourneyData({ dispatch }: any) {
    try {
      await dispatch('initializePersistence');

      if (preferencesManager) {
        await preferencesManager.clearAllData();
      }
    } catch (error) {
      console.error('Failed to clear journey data:', error);
      throw error;
    }
  },

  // System State Management
  updateSystemState({ commit }: any, state: Record<string, any>) {
    commit('UPDATE_SYSTEM_STATE', state);
  },

  async checkTriggers({ state, getters, dispatch }: any, { event, context }: { event: string, context?: any }) {
    if (!state.config.enableJourneys || !state.config.autoTrigger) return;

    const triggeredJourney = getters.shouldTriggerJourney(event, context);
    if (triggeredJourney && !state.isActive) {
      await dispatch('startJourney', { journeyId: triggeredJourney.id, context });
    }
  },

  // Configuration
  setConfig({ commit }: any, config: Partial<JourneyState['config']>) {
    commit('SET_JOURNEY_CONFIG', config);
  },

  // Utility Actions
  async buildJourneyContext({ state, rootState, rootGetters }: any, { journey, progress, context }: any): Promise<JourneyContext> {
    return {
      journey,
      progress,
      user: rootGetters['auth/user'],
      route: rootState.route?.current,
      store: rootState,
      systemState: state.systemState,
      ...context,
    };
  },

  emitJourneyEvent({ state }: any, payload: Omit<JourneyEventPayload, 'timestamp'>) {
    const event: JourneyEventPayload = {
      ...payload,
      timestamp: new Date(),
    };

    // Emit to analytics if enabled
    if (state.config.enableAnalytics) {
      // TODO: Send to analytics endpoint
      console.log('Journey Event:', event);
    }

    // Emit to Vue event bus or other listeners
    // TODO: Implement event emission system
  },

  // Cache Management
  clearCache({ commit }: any) {
    commit('CLEAR_CACHE');
  },

  // Error Handling
  clearErrors({ commit }: any) {
    commit('CLEAR_ERRORS');
  },

  // UI State Management
  setJourneyOverlayVisible({ commit }: any, visible: boolean) {
    commit('SET_JOURNEY_OVERLAY_VISIBLE', visible);
  },

  setTourMode({ commit }: any, tourMode: boolean) {
    commit('SET_TOUR_MODE', tourMode);
  },

  updateLastInteraction({ commit }: any) {
    commit('UPDATE_LAST_INTERACTION');
  },
};
