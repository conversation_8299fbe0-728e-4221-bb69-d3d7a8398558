/**
 * Journey Persistence Test Utility
 * 
 * Simple test to verify the persistence system is working correctly
 */

import type { JourneyProgress } from '../../types/journey';

export async function testPersistence(store: any): Promise<boolean> {
  try {
    console.log('Testing Journey Persistence System...');

    // Initialize persistence
    await store.dispatch('journeys/initializePersistence');
    console.log('✓ Persistence initialized');

    // Create test progress
    const testProgress: JourneyProgress = {
      journeyId: 'test-journey',
      status: 'in-progress',
      currentStepId: 'step-1',
      currentStepIndex: 0,
      completedSteps: [],
      decisions: {},
      startedAt: new Date(),
      lastActiveAt: new Date(),
      metadata: { test: true },
    };

    // Test saving progress
    await store.dispatch('journeys/saveProgress', testProgress);
    console.log('✓ Progress saved');

    // Test loading progress
    const loadedProgress = await store.dispatch('journeys/loadProgress');
    console.log('✓ Progress loaded:', loadedProgress);

    // Test config save/load
    const testConfig = {
      enableJourneys: true,
      enableAnalytics: false,
      debugMode: true,
    };

    await store.dispatch('journeys/saveConfig', testConfig);
    console.log('✓ Config saved');

    await store.dispatch('journeys/loadConfig');
    console.log('✓ Config loaded');

    // Test system state save/load
    const testSystemState = {
      clusterCount: 1,
      credentialCount: 2,
      isFirstLogin: false,
    };

    await store.dispatch('journeys/saveSystemState', testSystemState);
    console.log('✓ System state saved');

    await store.dispatch('journeys/loadSystemState');
    console.log('✓ System state loaded');

    console.log('✅ All persistence tests passed!');
    return true;

  } catch (error) {
    console.error('❌ Persistence test failed:', error);
    return false;
  }
}

export async function testPreferencesDirectly(store: any): Promise<boolean> {
  try {
    console.log('Testing Preferences API directly...');

    // Test direct prefs access
    await store.dispatch('prefs/set', {
      key: 'test-journey-pref',
      value: { test: 'data', timestamp: new Date() },
    }, { root: true });
    console.log('✓ Direct prefs set');

    const value = store.getters['prefs/get']('test-journey-pref');
    console.log('✓ Direct prefs get:', value);

    // Clean up
    await store.dispatch('prefs/unset', {
      key: 'test-journey-pref',
    }, { root: true });
    console.log('✓ Direct prefs cleanup');

    console.log('✅ Direct preferences test passed!');
    return true;

  } catch (error) {
    console.error('❌ Direct preferences test failed:', error);
    return false;
  }
}

export async function runAllTests(store: any): Promise<void> {
  console.log('🧪 Running Journey Persistence Tests...');
  
  const prefsTest = await testPreferencesDirectly(store);
  const persistenceTest = await testPersistence(store);
  
  if (prefsTest && persistenceTest) {
    console.log('🎉 All tests passed! Journey persistence is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Check the logs above for details.');
  }
}
