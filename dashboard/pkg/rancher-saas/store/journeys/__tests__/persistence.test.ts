/**
 * Journey Persistence Tests
 * 
 * Test suite for journey progress persistence functionality
 */

import { JourneyPersistence } from '../persistence';
import { createPreferencesManager } from '../preferences';
import type { JourneyProgress } from '../../../types/journey';

// Mock store for testing
const createMockStore = () => ({
  dispatch: jest.fn(),
  getters: {
    'prefs/get': jest.fn(),
  },
  state: {
    journeys: {
      userProgress: {},
      config: {},
      analytics: {},
      systemState: {},
    },
  },
});

// Mock localStorage
const mockLocalStorage = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
  };
})();

// Mock sessionStorage
const mockSessionStorage = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
  };
})();

// Setup global mocks
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage,
});

describe('JourneyPersistence', () => {
  let mockStore: any;
  let persistence: JourneyPersistence;

  beforeEach(() => {
    mockStore = createMockStore();
    persistence = new JourneyPersistence(mockStore, {
      useUserPreferences: true,
      useLocalStorage: true,
      useSessionStorage: true,
      syncInterval: 0, // Disable periodic sync for tests
    });

    // Clear all mocks
    jest.clearAllMocks();
    mockLocalStorage.clear();
    mockSessionStorage.clear();
  });

  afterEach(() => {
    persistence.destroy();
  });

  describe('saveProgress', () => {
    const mockProgress: JourneyProgress = {
      journeyId: 'test-journey',
      status: 'in-progress',
      currentStepId: 'step-1',
      currentStepIndex: 0,
      completedSteps: [],
      decisions: {},
      startedAt: new Date(),
      lastActiveAt: new Date(),
    };

    it('should save to user preferences when available', async () => {
      mockStore.dispatch.mockResolvedValue(undefined);

      const result = await persistence.saveProgress(mockProgress);

      expect(result.success).toBe(true);
      expect(result.source).toBe('user-preferences');
      expect(mockStore.dispatch).toHaveBeenCalledWith(
        'prefs/set',
        expect.objectContaining({
          key: 'journey-progress',
          value: expect.objectContaining({
            'test-journey': mockProgress,
          }),
        }),
        { root: true }
      );
    });

    it('should fallback to localStorage when user preferences fail', async () => {
      mockStore.dispatch.mockRejectedValue(new Error('Prefs failed'));

      const result = await persistence.saveProgress(mockProgress);

      expect(result.success).toBe(true);
      expect(result.source).toBe('localStorage');
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'rancher-journey-progress',
        expect.stringContaining('test-journey')
      );
    });

    it('should fallback to sessionStorage when localStorage fails', async () => {
      mockStore.dispatch.mockRejectedValue(new Error('Prefs failed'));
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('localStorage failed');
      });

      const result = await persistence.saveProgress(mockProgress);

      expect(result.success).toBe(true);
      expect(result.source).toBe('sessionStorage');
      expect(mockSessionStorage.setItem).toHaveBeenCalledWith(
        'rancher-journey-progress',
        expect.stringContaining('test-journey')
      );
    });

    it('should return failure when all methods fail', async () => {
      mockStore.dispatch.mockRejectedValue(new Error('Prefs failed'));
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('localStorage failed');
      });
      mockSessionStorage.setItem.mockImplementation(() => {
        throw new Error('sessionStorage failed');
      });

      const result = await persistence.saveProgress(mockProgress);

      expect(result.success).toBe(false);
      expect(result.source).toBe('none');
      expect(result.error).toBeInstanceOf(Error);
    });
  });

  describe('loadProgress', () => {
    const mockProgressData = {
      'test-journey': {
        journeyId: 'test-journey',
        status: 'completed',
        completedSteps: ['step-1', 'step-2'],
      },
    };

    it('should load from user preferences when available', async () => {
      mockStore.getters['prefs/get'].mockReturnValue(mockProgressData);

      const result = await persistence.loadProgress();

      expect(result).toEqual(mockProgressData);
      expect(mockStore.getters['prefs/get']).toHaveBeenCalledWith('journey-progress');
    });

    it('should load specific journey from user preferences', async () => {
      mockStore.getters['prefs/get'].mockReturnValue(mockProgressData);

      const result = await persistence.loadProgress('test-journey');

      expect(result).toEqual(mockProgressData['test-journey']);
    });

    it('should fallback to localStorage when user preferences fail', async () => {
      mockStore.getters['prefs/get'].mockReturnValue(null);
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockProgressData));

      const result = await persistence.loadProgress();

      expect(result).toEqual(mockProgressData);
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('rancher-journey-progress');
    });

    it('should fallback to sessionStorage when localStorage fails', async () => {
      mockStore.getters['prefs/get'].mockReturnValue(null);
      mockLocalStorage.getItem.mockReturnValue(null);
      mockSessionStorage.getItem.mockReturnValue(JSON.stringify(mockProgressData));

      const result = await persistence.loadProgress();

      expect(result).toEqual(mockProgressData);
      expect(mockSessionStorage.getItem).toHaveBeenCalledWith('rancher-journey-progress');
    });

    it('should return null when no data is found', async () => {
      mockStore.getters['prefs/get'].mockReturnValue(null);
      mockLocalStorage.getItem.mockReturnValue(null);
      mockSessionStorage.getItem.mockReturnValue(null);

      const result = await persistence.loadProgress();

      expect(result).toBeNull();
    });
  });
});

describe('RancherPreferencesManager', () => {
  let mockStore: any;
  let preferencesManager: any;

  beforeEach(() => {
    mockStore = createMockStore();
    preferencesManager = createPreferencesManager(mockStore);

    jest.clearAllMocks();
  });

  describe('saveProgress', () => {
    const mockProgress: JourneyProgress = {
      journeyId: 'test-journey',
      status: 'in-progress',
      currentStepId: 'step-1',
      currentStepIndex: 0,
      completedSteps: [],
      decisions: {},
      startedAt: new Date(),
      lastActiveAt: new Date(),
    };

    it('should save progress to user preferences', async () => {
      mockStore.dispatch.mockResolvedValue(undefined);

      await preferencesManager.saveProgress(mockProgress);

      expect(mockStore.dispatch).toHaveBeenCalledWith('prefs/set', {
        key: 'journey-progress',
        value: expect.objectContaining({
          'test-journey': expect.objectContaining({
            journeyId: 'test-journey',
            status: 'in-progress',
          }),
        }),
      });
    });

    it('should update lastActiveAt when saving', async () => {
      mockStore.dispatch.mockResolvedValue(undefined);
      const originalTime = mockProgress.lastActiveAt;

      await preferencesManager.saveProgress(mockProgress);

      const savedData = mockStore.dispatch.mock.calls[0][1].value;
      expect(savedData['test-journey'].lastActiveAt).toBeInstanceOf(Date);
      expect(savedData['test-journey'].lastActiveAt.getTime()).toBeGreaterThanOrEqual(originalTime.getTime());
    });
  });

  describe('loadProgress', () => {
    const mockProgressData = {
      'test-journey': {
        journeyId: 'test-journey',
        status: 'completed',
      },
    };

    it('should load all progress when no journeyId specified', async () => {
      mockStore.getters['prefs/get'].mockReturnValue(mockProgressData);

      const result = await preferencesManager.loadProgress();

      expect(result).toEqual(mockProgressData);
    });

    it('should load specific journey progress when journeyId specified', async () => {
      mockStore.getters['prefs/get'].mockReturnValue(mockProgressData);

      const result = await preferencesManager.loadProgress('test-journey');

      expect(result).toEqual(mockProgressData['test-journey']);
    });

    it('should return null when no progress found', async () => {
      mockStore.getters['prefs/get'].mockReturnValue(null);

      const result = await preferencesManager.loadProgress();

      expect(result).toBeNull();
    });
  });

  describe('exportData', () => {
    it('should export all journey data', async () => {
      const mockData = {
        PROGRESS: { 'journey-1': { status: 'completed' } },
        CONFIG: { enableJourneys: true },
        ANALYTICS: { journeyStartCount: { 'journey-1': 5 } },
      };

      mockStore.getters['prefs/get']
        .mockReturnValueOnce(mockData.PROGRESS)
        .mockReturnValueOnce(mockData.CONFIG)
        .mockReturnValueOnce(mockData.ANALYTICS)
        .mockReturnValue(null); // For other preferences

      const result = await preferencesManager.exportData();

      expect(result).toEqual(expect.objectContaining({
        PROGRESS: mockData.PROGRESS,
        CONFIG: mockData.CONFIG,
        ANALYTICS: mockData.ANALYTICS,
        exportedAt: expect.any(String),
        version: '1.0.0',
      }));
    });
  });

  describe('clearAllData', () => {
    it('should clear all journey preferences', async () => {
      mockStore.dispatch.mockResolvedValue(undefined);

      await preferencesManager.clearAllData();

      // Should call unset for each preference key
      expect(mockStore.dispatch).toHaveBeenCalledTimes(6); // Number of preference keys
      expect(mockStore.dispatch).toHaveBeenCalledWith('prefs/unset', { key: 'journey-progress' });
      expect(mockStore.dispatch).toHaveBeenCalledWith('prefs/unset', { key: 'journey-config' });
    });
  });
});
