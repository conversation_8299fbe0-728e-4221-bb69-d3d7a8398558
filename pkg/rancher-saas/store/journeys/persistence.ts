/**
 * Journey Progress Persistence
 * 
 * This module handles saving and loading journey progress using <PERSON><PERSON>'s user preferences API
 * with fallback to localStorage and session storage for offline scenarios.
 */

import type { JourneyProgress, JourneyState } from '../../types/journey';

// Preference keys for different types of journey data
export const JOURNEY_PREFERENCE_KEYS = {
  PROGRESS: 'journey-progress',
  ANALYTICS: 'journey-analytics',
  CONFIG: 'journey-config',
  SYSTEM_STATE: 'journey-system-state',
  COMPLETED_JOURNEYS: 'journey-completed',
} as const;

// Storage fallback keys
export const STORAGE_KEYS = {
  PROGRESS: 'rancher-journey-progress',
  ANALYTICS: 'rancher-journey-analytics',
  CONFIG: 'rancher-journey-config',
  SYSTEM_STATE: 'rancher-journey-system-state',
  COMPLETED_JOURNEYS: 'rancher-journey-completed',
  LAST_SYNC: 'rancher-journey-last-sync',
} as const;

export interface PersistenceOptions {
  useUserPreferences?: boolean;
  useLocalStorage?: boolean;
  useSessionStorage?: boolean;
  syncInterval?: number; // milliseconds
  maxRetries?: number;
  compressionEnabled?: boolean;
}

export interface SyncResult {
  success: boolean;
  source: 'user-preferences' | 'localStorage' | 'sessionStorage' | 'none';
  timestamp: Date;
  error?: Error;
}

export class JourneyPersistence {
  private store: any;
  private options: PersistenceOptions;
  private syncTimer: NodeJS.Timeout | null = null;
  private lastSyncTime: Date | null = null;

  constructor(store: any, options: PersistenceOptions = {}) {
    this.store = store;
    this.options = {
      useUserPreferences: true,
      useLocalStorage: true,
      useSessionStorage: false,
      syncInterval: 30000, // 30 seconds
      maxRetries: 3,
      compressionEnabled: false,
      ...options,
    };
  }

  /**
   * Initialize persistence system
   */
  async initialize(): Promise<void> {
    try {
      // Load existing progress
      await this.loadAllProgress();
      
      // Start periodic sync if enabled
      if (this.options.syncInterval && this.options.syncInterval > 0) {
        this.startPeriodicSync();
      }

      console.log('Journey persistence initialized');
    } catch (error) {
      console.error('Failed to initialize journey persistence:', error);
      throw error;
    }
  }

  /**
   * Save journey progress with multiple fallback strategies
   */
  async saveProgress(progress: JourneyProgress): Promise<SyncResult> {
    const attempts: Array<() => Promise<SyncResult>> = [];

    // Primary: User Preferences API
    if (this.options.useUserPreferences) {
      attempts.push(() => this.saveToUserPreferences(progress));
    }

    // Fallback: localStorage
    if (this.options.useLocalStorage) {
      attempts.push(() => this.saveToLocalStorage(progress));
    }

    // Last resort: sessionStorage
    if (this.options.useSessionStorage) {
      attempts.push(() => this.saveToSessionStorage(progress));
    }

    // Try each method in order
    for (const attempt of attempts) {
      try {
        const result = await attempt();
        if (result.success) {
          this.lastSyncTime = new Date();
          return result;
        }
      } catch (error) {
        console.warn('Persistence attempt failed:', error);
        continue;
      }
    }

    return {
      success: false,
      source: 'none',
      timestamp: new Date(),
      error: new Error('All persistence methods failed'),
    };
  }

  /**
   * Load journey progress from available sources
   */
  async loadProgress(journeyId?: string): Promise<Record<string, JourneyProgress> | JourneyProgress | null> {
    const attempts: Array<() => Promise<any>> = [];

    // Try user preferences first
    if (this.options.useUserPreferences) {
      attempts.push(() => this.loadFromUserPreferences(journeyId));
    }

    // Fallback to localStorage
    if (this.options.useLocalStorage) {
      attempts.push(() => this.loadFromLocalStorage(journeyId));
    }

    // Last resort: sessionStorage
    if (this.options.useSessionStorage) {
      attempts.push(() => this.loadFromSessionStorage(journeyId));
    }

    for (const attempt of attempts) {
      try {
        const result = await attempt();
        if (result) {
          return result;
        }
      } catch (error) {
        console.warn('Load attempt failed:', error);
        continue;
      }
    }

    return null;
  }

  /**
   * Save to Rancher User Preferences API
   */
  private async saveToUserPreferences(progress: JourneyProgress): Promise<SyncResult> {
    try {
      // Get current progress data
      const currentProgress = await this.loadFromUserPreferences() || {};
      currentProgress[progress.journeyId] = progress;

      // Save via Rancher's prefs system
      await this.store.dispatch('prefs/set', {
        key: JOURNEY_PREFERENCE_KEYS.PROGRESS,
        value: currentProgress,
      }, { root: true });

      return {
        success: true,
        source: 'user-preferences',
        timestamp: new Date(),
      };
    } catch (error) {
      throw new Error(`User preferences save failed: ${error.message}`);
    }
  }

  /**
   * Load from Rancher User Preferences API
   */
  private async loadFromUserPreferences(journeyId?: string): Promise<Record<string, JourneyProgress> | JourneyProgress | null> {
    try {
      const prefs = this.store.getters['prefs/get'](JOURNEY_PREFERENCE_KEYS.PROGRESS);
      
      if (!prefs) return null;

      if (journeyId) {
        return prefs[journeyId] || null;
      }

      return prefs;
    } catch (error) {
      throw new Error(`User preferences load failed: ${error.message}`);
    }
  }

  /**
   * Save to localStorage
   */
  private async saveToLocalStorage(progress: JourneyProgress): Promise<SyncResult> {
    try {
      if (!this.isStorageAvailable('localStorage')) {
        throw new Error('localStorage not available');
      }

      const currentProgress = this.loadFromLocalStorageSync() || {};
      currentProgress[progress.journeyId] = progress;

      const data = this.options.compressionEnabled ? 
        this.compressData(currentProgress) : 
        JSON.stringify(currentProgress);

      localStorage.setItem(STORAGE_KEYS.PROGRESS, data);
      localStorage.setItem(STORAGE_KEYS.LAST_SYNC, new Date().toISOString());

      return {
        success: true,
        source: 'localStorage',
        timestamp: new Date(),
      };
    } catch (error) {
      throw new Error(`localStorage save failed: ${error.message}`);
    }
  }

  /**
   * Load from localStorage
   */
  private async loadFromLocalStorage(journeyId?: string): Promise<Record<string, JourneyProgress> | JourneyProgress | null> {
    try {
      const data = this.loadFromLocalStorageSync();
      
      if (!data) return null;

      if (journeyId) {
        return data[journeyId] || null;
      }

      return data;
    } catch (error) {
      throw new Error(`localStorage load failed: ${error.message}`);
    }
  }

  /**
   * Synchronous localStorage load
   */
  private loadFromLocalStorageSync(): Record<string, JourneyProgress> | null {
    try {
      if (!this.isStorageAvailable('localStorage')) {
        return null;
      }

      const data = localStorage.getItem(STORAGE_KEYS.PROGRESS);
      if (!data) return null;

      return this.options.compressionEnabled ? 
        this.decompressData(data) : 
        JSON.parse(data);
    } catch (error) {
      console.warn('Failed to load from localStorage:', error);
      return null;
    }
  }

  /**
   * Save to sessionStorage
   */
  private async saveToSessionStorage(progress: JourneyProgress): Promise<SyncResult> {
    try {
      if (!this.isStorageAvailable('sessionStorage')) {
        throw new Error('sessionStorage not available');
      }

      const currentProgress = this.loadFromSessionStorageSync() || {};
      currentProgress[progress.journeyId] = progress;

      const data = JSON.stringify(currentProgress);
      sessionStorage.setItem(STORAGE_KEYS.PROGRESS, data);

      return {
        success: true,
        source: 'sessionStorage',
        timestamp: new Date(),
      };
    } catch (error) {
      throw new Error(`sessionStorage save failed: ${error.message}`);
    }
  }

  /**
   * Load from sessionStorage
   */
  private async loadFromSessionStorage(journeyId?: string): Promise<Record<string, JourneyProgress> | JourneyProgress | null> {
    try {
      const data = this.loadFromSessionStorageSync();
      
      if (!data) return null;

      if (journeyId) {
        return data[journeyId] || null;
      }

      return data;
    } catch (error) {
      throw new Error(`sessionStorage load failed: ${error.message}`);
    }
  }

  /**
   * Synchronous sessionStorage load
   */
  private loadFromSessionStorageSync(): Record<string, JourneyProgress> | null {
    try {
      if (!this.isStorageAvailable('sessionStorage')) {
        return null;
      }

      const data = sessionStorage.getItem(STORAGE_KEYS.PROGRESS);
      if (!data) return null;

      return JSON.parse(data);
    } catch (error) {
      console.warn('Failed to load from sessionStorage:', error);
      return null;
    }
  }

  /**
   * Load all progress from all available sources
   */
  private async loadAllProgress(): Promise<void> {
    try {
      const progress = await this.loadProgress();
      
      if (progress && typeof progress === 'object') {
        // Load progress into store
        Object.keys(progress).forEach(journeyId => {
          this.store.commit('journeys/SET_USER_PROGRESS', {
            journeyId,
            progress: progress[journeyId],
          });
        });
      }
    } catch (error) {
      console.error('Failed to load all progress:', error);
    }
  }

  /**
   * Start periodic sync to keep data fresh
   */
  private startPeriodicSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }

    this.syncTimer = setInterval(async () => {
      try {
        await this.syncProgress();
      } catch (error) {
        console.warn('Periodic sync failed:', error);
      }
    }, this.options.syncInterval);
  }

  /**
   * Sync progress between different storage methods
   */
  private async syncProgress(): Promise<void> {
    const state = this.store.state.journeys as JourneyState;
    
    // Save current progress for all active journeys
    Object.values(state.userProgress).forEach(async (progress) => {
      if (progress.status === 'in-progress') {
        await this.saveProgress(progress);
      }
    });
  }

  /**
   * Check if storage is available
   */
  private isStorageAvailable(type: 'localStorage' | 'sessionStorage'): boolean {
    try {
      const storage = window[type];
      const test = '__storage_test__';
      storage.setItem(test, test);
      storage.removeItem(test);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Compress data for storage (simple implementation)
   */
  private compressData(data: any): string {
    // Simple compression - in production, consider using a proper compression library
    return JSON.stringify(data);
  }

  /**
   * Decompress data from storage
   */
  private decompressData(data: string): any {
    return JSON.parse(data);
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
  }
}
